﻿using Avalonia.Controls;
using Avalonia.Controls.Notifications;
using Avalonia.Interactivity;
using MsBox.Avalonia;
using MsBox.Avalonia.Dto;
using MsBox.Avalonia.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using y4eb_praktika.Models;

namespace y4eb_praktika.Pages.Deals
{
    public class DealViewModel
    {
        public int Id { get; set; }
        public int DemandId { get; set; }
        public int SupplyId { get; set; }
        // Продавец
        public string SellerClient { get; set; } = "";
        public string SellerAgent { get; set; } = "";

        // Покупатель
        public string BuyerClient { get; set; } = "";
        public string BuyerAgent { get; set; } = "";

        // Объект и цена
        public string RealEstateInfo { get; set; } = "";
        public int SalePrice { get; set; }

        // Комиссии
        public double SellerCommission { get; set; }
        public double BuyerCommission { get; set; }
        public double SellerRealtorShare { get; set; }
        public double BuyerRealtorShare { get; set; }
        public double CompanyShare { get; set; }
    }

    public partial class DealsWindow : Window
    {
        public ObservableCollection<DealViewModel> DealsList { get; } = new();
        private List<DealViewModel> _allDeals = new();
        private TextBox? _searchInput;
        private DealEditWindow? _editWindow;
        private QuickDealWindow? _quickDealWindow;
        private const double DEFAULT_REALTOR_SHARE = 0.45;

        public DealsWindow()
        {
            InitializeComponent();
            DataContext = this;
            LoadDeals();
            this.Opened += DealsWindow_Opened;
            var searchBtn = this.FindControl<Button>("SearchButton");
            if (searchBtn != null)
                searchBtn.Click += SearchButton_Click;
        }

        private void DealsWindow_Opened(object? sender, EventArgs e)
        {
            _searchInput = this.FindControl<UserControl>("SearchBox")
                              ?.FindControl<TextBox>("PART_Input");
            if (_searchInput != null)
                _searchInput.TextChanged += (_, __) => ApplyFilter();
        }

        private void LoadDeals()
        {
            using var db = MyDbContext.GetContext();

            var deals = db.Deals
                .Include(d => d.Demand!).ThenInclude(d => d.Client)
                .Include(d => d.Demand).ThenInclude(d => d.Agent)
                .Include(d => d.Supply).ThenInclude(s => s.Client)
                .Include(d => d.Supply).ThenInclude(s => s.Agent)
                .Include(d => d.Supply).ThenInclude(s => s.RealEstate)
                    .ThenInclude(r => r.Apartment)
                .Include(d => d.Supply).ThenInclude(s => s.RealEstate)
                    .ThenInclude(r => r.House)
                .Include(d => d.Supply).ThenInclude(s => s.RealEstate)
                    .ThenInclude(r => r.Land)
                .ToList();

            _allDeals = deals.Select(d =>
            {
                // Базовые данные
                var sellerC = d.Demand!.Client!;
                var sellerA = d.Demand.Agent!;
                var buyerC = d.Supply!.Client!;
                var buyerA = d.Supply.Agent!;
                var price = d.Supply.Price;

                // Тип недвижимости и описание
                var re = d.Supply.RealEstate!;
                string reInfo = re.Apartment is not null
                    ? $"Кв.: {re.Apartment.Address_City}, {re.Apartment.Address_Street} {re.Apartment.Address_House}, кв.{re.Apartment.Address_Number}"
                    : re.House is not null
                        ? $"Дом: {re.House.Address_City}, {re.House.Address_Street} {re.House.Address_House}"
                        : re.Land is not null
                            ? $"Земля: {re.Land.Address_City}, {re.Land.Address_Street} {re.Land.Address_House}"
                            : "";

                // Комиссия продавца по типу
                double sellerComm = re.Apartment != null
                    ? 36000 + 0.01 * price
                    : re.Land != null
                        ? 30000 + 0.02 * price
                        : 30000 + 0.01 * price;
                // Комиссия покупателя = 3%
                double buyerComm = 0.03 * price;

                // Доля риэлторов (или default 45%)
                var sellerSharePct = (sellerA.DealShare ?? (DEFAULT_REALTOR_SHARE * 100)) / 100.0;
                var buyerSharePct = (buyerA.DealShare ?? (DEFAULT_REALTOR_SHARE * 100)) / 100.0;

                double sellerRealtor = sellerComm * sellerSharePct;
                double buyerRealtor = buyerComm * buyerSharePct;
                double companyPortion = (sellerComm - sellerRealtor)
                                       + (buyerComm - buyerRealtor);

                return new DealViewModel
                {
                    Id = d.Demand_Id,
                    SellerClient = $"{sellerC.MiddleName} {sellerC.LastName}",
                    SellerAgent = $"{sellerA.MiddleName} {sellerA.LastName}",
                    BuyerClient = $"{buyerC.MiddleName} {buyerC.LastName}",
                    BuyerAgent = $"{buyerA.MiddleName} {buyerA.LastName}",
                    RealEstateInfo = reInfo,
                    SalePrice = price,
                    SellerCommission = Math.Round(sellerComm, 2),
                    BuyerCommission = Math.Round(buyerComm, 2),
                    SellerRealtorShare = Math.Round(sellerRealtor, 2),
                    BuyerRealtorShare = Math.Round(buyerRealtor, 2),
                    CompanyShare = Math.Round(companyPortion, 2)
                };
            })
            .OrderBy(x => x.SellerClient)
            .ToList();

            DealsList.Clear();
            foreach (var vm in _allDeals)
                DealsList.Add(vm);

            DealsGrid.ItemsSource = DealsList;
        }

        private void ApplyFilter()
        {
            var txt = _searchInput?.Text?.Trim() ?? "";
            DealsList.Clear();

            if (string.IsNullOrEmpty(txt))
            {
                foreach (var vm in _allDeals)
                    DealsList.Add(vm);
            }
            else
            {
                var filtered = _allDeals.Where(vm =>
                       vm.SellerClient.Contains(txt, StringComparison.OrdinalIgnoreCase)
                    || vm.BuyerClient.Contains(txt, StringComparison.OrdinalIgnoreCase)
                    || vm.SellerAgent.Contains(txt, StringComparison.OrdinalIgnoreCase)
                    || vm.BuyerAgent.Contains(txt, StringComparison.OrdinalIgnoreCase)
                    || vm.RealEstateInfo.Contains(txt, StringComparison.OrdinalIgnoreCase)
                );
                foreach (var vm in filtered)
                    DealsList.Add(vm);
            }
        }

        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is not Button btn || btn.Tag is not DealViewModel vm) return;

            var custom = new MessageBoxCustomParams
            {
                ContentTitle = "Подтверждение",
                ContentMessage = "Удалить эту сделку?",
                ButtonDefinitions = new[]
                {
                    new ButtonDefinition { Name = "Да", IsDefault = true },
                    new ButtonDefinition { Name = "Нет", IsCancel = true }
                },
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                Icon = MsBox.Avalonia.Enums.Icon.Question
            };
            var msgBox = MessageBoxManager.GetMessageBoxCustom(custom);
            var res = await msgBox.ShowAsync();
            if (res != "Да") return;

            using var db = MyDbContext.GetContext();
            var deal = db.Deals.Find(vm.Id);
            if (deal == null) return;

            db.Deals.Remove(deal);
            db.SaveChanges();

            LoadDeals();
            new WindowNotificationManager(this)
            {
                Position = NotificationPosition.BottomLeft
            }
            .Show(new Notification("Удалено", "Сделка удалена", NotificationType.Success));
        }

        private async void EditButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is not Button btn || btn.Tag is not DealViewModel vm)
                    return;

                // Проверяем, что окно не открыто уже
                if (_editWindow != null)
                    return;

                // Создаем новое окно каждый раз
                _editWindow = new DealEditWindow();

                try
                {
                    _editWindow.Initialize(vm.DemandId, vm.SupplyId);
                    var result = await _editWindow.ShowDialog<bool?>(this);

                    if (result == true)
                    {
                        LoadDeals();
                        new WindowNotificationManager(this)
                        {
                            Position = NotificationPosition.BottomLeft
                        }
                        .Show(new Notification("Успех", "Сделка обновлена", NotificationType.Success));
                    }
                }
                finally
                {
                    _editWindow = null; // Очищаем ссылку после закрытия
                }
            }
            catch (Exception ex)
            {
                _editWindow = null; // Очищаем ссылку в случае ошибки
                new WindowNotificationManager(this)
                {
                    Position = NotificationPosition.BottomLeft
                }
                .Show(new Notification("Ошибка", $"Ошибка при редактировании: {ex.Message}", NotificationType.Error));
            }
        }

        private async void AddButton_Click(object sender, RoutedEventArgs e)
        {
            var addWin = new DealAddWindow();
            var result = await addWin.ShowDialog<bool?>(this);
            if (result == true)
            {
                LoadDeals();
                new WindowNotificationManager(this)
                {
                    Position = NotificationPosition.BottomLeft
                }
                .Show(new Notification("Успех", "Новая сделка добавлена", NotificationType.Success));
            }
        }

        private void BackButton_Click(object? sender, RoutedEventArgs e)
        {
            new MainWindow().Show();
            this.Close();
        }

        private async void SearchButton_Click(object? sender, RoutedEventArgs e)
        {
            try
            {
                // Проверяем, что окно не открыто уже
                if (_quickDealWindow != null)
                    return;

                _quickDealWindow = new y4eb_praktika.Pages.Deals.QuickDealWindow();

                try
                {
                    var result = await _quickDealWindow.ShowDialog<bool?>(this);
                    if (result == true)
                    {
                        LoadDeals();
                        new WindowNotificationManager(this)
                        {
                            Position = NotificationPosition.BottomLeft
                        }
                        .Show(new Notification("Успех", "Сделка создана", NotificationType.Success));
                    }
                }
                finally
                {
                    _quickDealWindow = null;
                }
            }
            catch (Exception ex)
            {
                new WindowNotificationManager(this)
                {
                    Position = NotificationPosition.BottomLeft
                }
                .Show(new Notification("Ошибка", $"Ошибка при открытии окна: {ex.Message}", NotificationType.Error));
            }
        }
    }
}
