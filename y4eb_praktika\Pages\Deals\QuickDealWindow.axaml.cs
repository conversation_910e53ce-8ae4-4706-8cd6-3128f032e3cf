using Avalonia.Controls;
using Avalonia.Controls.Notifications;
using Avalonia.Interactivity;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using y4eb_praktika.Models;

namespace y4eb_praktika.Pages.Deals
{
    public partial class QuickDealWindow : Window
    {
        private readonly WindowNotificationManager _notif;
        public ObservableCollection<DemandViewModel> Demands { get; } = new();
        public ObservableCollection<SupplyViewModel> Supplies { get; } = new();

        public class SupplyLookup
        {
            public int Id { get; }
            public string Text { get; }
            public SupplyLookup(int id, string text) { Id = id; Text = text; }
            public override string ToString() => Text;
        }

        public class DemandLookup
        {
            public int Id { get; }
            public string Text { get; }
            public DemandLookup(int id, string text) { Id = id; Text = text; }
            public override string ToString() => Text;
        }

        public class DemandViewModel
        {
            public int Id { get; set; }
            public string Client { get; set; } = "";
            public string Agent { get; set; } = "";
            public int? MinPrice { get; set; }
            public int? MaxPrice { get; set; }
            public string TypeName { get; set; } = "";
        }

        public class SupplyViewModel
        {
            public int Id { get; set; }
            public string Client { get; set; } = "";
            public string Agent { get; set; } = "";
            public int Price { get; set; }
            public string TypeName { get; set; } = "";
        }

        public QuickDealWindow()
        {
            InitializeComponent();
            _notif = new WindowNotificationManager(this)
            {
                Position = NotificationPosition.BottomLeft,
                MaxItems = 3
            };

            // Инициализация обработчиков событий
            SupplySearchMode.IsCheckedChanged += SearchMode_Changed;
            DemandSearchMode.IsCheckedChanged += SearchMode_Changed;
            SupplyComboBox.SelectionChanged += SupplyComboBox_SelectionChanged;
            DemandComboBox.SelectionChanged += DemandComboBox_SelectionChanged;

            // Загрузка начальных данных
            LoadSupplies();
            LoadDemands();
            DemandGrid.ItemsSource = Demands;
            SupplyGrid.ItemsSource = Supplies;
        }

        private void SearchMode_Changed(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            if (sender is RadioButton rb)
            {
                SupplySearchPanel.IsVisible = rb == SupplySearchMode;
                DemandSearchPanel.IsVisible = rb == DemandSearchMode;
            }
        }

        private void LoadSupplies()
        {
            using var db = MyDbContext.GetContext();
            var supplies = db.Supplies
                .Include(s => s.Client).Include(s => s.Agent)
                .Include(s => s.RealEstate).ThenInclude(r => r.Apartment)
                .Include(s => s.RealEstate).ThenInclude(r => r.House)
                .Include(s => s.RealEstate).ThenInclude(r => r.Land)
                .Where(s => s.Deal == null)
                .Select(s => new SupplyLookup(
                    s.Id,
                    $"#{s.Id} - Клиент:{s.Client.MiddleName} {s.Client.LastName} - " +
                    $"Агент:{s.Agent.MiddleName} {s.Agent.LastName} - Цена:{s.Price}"
                ))
                .ToList();
            SupplyComboBox.ItemsSource = supplies;
        }

        private void LoadDemands()
        {
            using var db = MyDbContext.GetContext();
            var demands = db.Demands
                .Include(d => d.Client).Include(d => d.Agent).Include(d => d.Type)
                .Where(d => d.Deal == null)
                .Select(d => new DemandLookup(
                    d.Id,
                    $"#{d.Id} - Клиент:{d.Client.MiddleName} {d.Client.LastName} - " +
                    $"Агент:{d.Agent.MiddleName} {d.Agent.LastName} - {d.Type.TypeName} - " +
                    $"[{d.MinPrice}-{d.MaxPrice}]"
                ))
                .ToList();
            DemandComboBox.ItemsSource = demands;
        }

        private void SupplyComboBox_SelectionChanged(object? sender, SelectionChangedEventArgs e)
        {
            Demands.Clear();
            SupplyError.IsVisible = false;
            var selected = SupplyComboBox.SelectedItem as SupplyLookup;
            if (selected == null)
                return;

            using var db = MyDbContext.GetContext();
            var supply = db.Supplies
                .Include(s => s.RealEstate)
                .FirstOrDefault(s => s.Id == selected.Id);
            if (supply == null)
                return;

            var demands = db.Demands
                .Include(d => d.Client).Include(d => d.Agent).Include(d => d.Type)
                .Where(d => d.Deal == null && d.MinPrice <= supply.Price && d.MaxPrice >= supply.Price)
                .ToList();

            foreach (var d in demands)
            {
                Demands.Add(new DemandViewModel
                {
                    Id = d.Id,
                    Client = $"{d.Client.MiddleName} {d.Client.LastName}",
                    Agent = $"{d.Agent.MiddleName} {d.Agent.LastName}",
                    MinPrice = d.MinPrice,
                    MaxPrice = d.MaxPrice,
                    TypeName = d.Type.TypeName
                });
            }
        }

        private void DemandComboBox_SelectionChanged(object? sender, SelectionChangedEventArgs e)
        {
            Supplies.Clear();
            DemandError.IsVisible = false;
            var selected = DemandComboBox.SelectedItem as DemandLookup;
            if (selected == null)
                return;

            using var db = MyDbContext.GetContext();
            var demand = db.Demands
                .Include(d => d.Type)
                .FirstOrDefault(d => d.Id == selected.Id);
            if (demand == null)
                return;

            var supplies = db.Supplies
                .Include(s => s.Client).Include(s => s.Agent)
                .Include(s => s.RealEstate)
                .Where(s => s.Deal == null && s.Price >= demand.MinPrice && s.Price <= demand.MaxPrice)
                .ToList();

            foreach (var s in supplies)
            {
                string typeName = s.RealEstate.Apartment != null ? "Квартира" :
                                s.RealEstate.House != null ? "Дом" :
                                s.RealEstate.Land != null ? "Участок" : "Неизвестно";

                Supplies.Add(new SupplyViewModel
                {
                    Id = s.Id,
                    Client = $"{s.Client.MiddleName} {s.Client.LastName}",
                    Agent = $"{s.Agent.MiddleName} {s.Agent.LastName}",
                    Price = s.Price,
                    TypeName = typeName
                });
            }
        }

        private async void CreateDealButton_Click(object? sender, RoutedEventArgs e)
        {
            var selectedSupply = SupplyComboBox.SelectedItem as SupplyLookup;
            if (selectedSupply == null)
            {
                SupplyError.Text = "Выберите предложение";
                SupplyError.IsVisible = true;
                return;
            }
            if (sender is not Button btn || btn.Tag is not DemandViewModel demand)
                return;

            await CreateDeal(demand.Id, selectedSupply.Id);
        }

        private async void CreateDealFromSupplyButton_Click(object? sender, RoutedEventArgs e)
        {
            var selectedDemand = DemandComboBox.SelectedItem as DemandLookup;
            if (selectedDemand == null)
            {
                DemandError.Text = "Выберите потребность";
                DemandError.IsVisible = true;
                return;
            }
            if (sender is not Button btn || btn.Tag is not SupplyViewModel supply)
                return;

            await CreateDeal(selectedDemand.Id, supply.Id);
        }

        private async Task CreateDeal(int demandId, int supplyId)
        {
            try
            {
                using var db = MyDbContext.GetContext();
                // Проверка, не заняты ли уже
                var existingDeal = db.Deals.FirstOrDefault(d => d.Demand_Id == demandId || d.Supply_Id == supplyId);
                if (existingDeal != null)
                {
                    _notif.Show(new Notification("Ошибка", "Потребность или предложение уже участвуют в другой сделке", NotificationType.Error));
                    return;
                }
                db.Deals.Add(new y4eb_praktika.Models.Deals { Demand_Id = demandId, Supply_Id = supplyId });
                db.SaveChanges();
                _notif.Show(new Notification("Успех", "Сделка создана", NotificationType.Success));
                Close(true);
            }
            catch (Exception ex)
            {
                _notif.Show(new Notification("Ошибка", $"Ошибка при создании сделки: {ex.Message}", NotificationType.Error));
            }
        }

        private void BackButton_Click(object? sender, RoutedEventArgs e)
        {
            Close(false);
        }
    }
} 